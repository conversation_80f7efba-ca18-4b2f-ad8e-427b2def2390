Log started at 8/4/2025 2:12:11 PM
2025-08-04 14:12:12.021 [Information] LoggingService: Logging service initialized
2025-08-04 14:12:12.055 [Information] App: Starting integrated application initialization
2025-08-04 14:12:12.070 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-08-04 14:12:12.071 [Information] X64LibraryResolver: X64LibraryResolver initialized for x64 process
2025-08-04 14:12:12.074 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-08-04 14:12:12.077 [Information] IntegratedStartupService: Setting up application environment
2025-08-04 14:12:12.078 [Information] IntegratedStartupService: Application environment setup completed
2025-08-04 14:12:12.081 [Information] IntegratedStartupService: Resolving x64 architecture library compatibility
2025-08-04 14:12:12.084 [Information] X64LibraryResolver: Starting x64 library resolution
2025-08-04 14:12:12.087 [Information] X64LibraryResolver: Resolving Visual C++ runtime libraries
2025-08-04 14:12:12.097 [Information] X64LibraryResolver: Downloading Visual C++ Redistributable for msvcr140.dll
2025-08-04 14:16:31.417 [Information] X64LibraryResolver: ✓ Installed Visual C++ Redistributable for msvcr140.dll
2025-08-04 14:16:33.420 [Warning] X64LibraryResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-04 14:16:33.421 [Information] X64LibraryResolver: ✓ Found compatible library: msvcp140.dll
2025-08-04 14:16:33.423 [Information] X64LibraryResolver: ✓ Found compatible library: vcruntime140.dll
2025-08-04 14:16:33.425 [Information] X64LibraryResolver: Analyzing APCI library compatibility
2025-08-04 14:16:33.427 [Information] X64LibraryResolver: Found APCI library: apci.dll (x86)
2025-08-04 14:16:33.428 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: apci.dll is x86, process is x64
2025-08-04 14:16:33.428 [Warning] X64LibraryResolver: ✗ Missing: apcidb.dll
2025-08-04 14:16:33.430 [Information] X64LibraryResolver: Found APCI library: Volvo.ApciPlus.dll (x86)
2025-08-04 14:16:33.430 [Warning] X64LibraryResolver: ⚠ Architecture mismatch: Volvo.ApciPlus.dll is x86, process is x64
2025-08-04 14:16:33.430 [Warning] X64LibraryResolver: ✗ Missing: Volvo.ApciPlusData.dll
2025-08-04 14:16:33.432 [Information] X64LibraryResolver: Setting up architecture bridge for x86 library compatibility
2025-08-04 14:16:33.432 [Warning] X64LibraryResolver: ⚠ Architecture bridge not found: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-04 14:16:33.434 [Information] X64LibraryResolver: Configuring environment for x64 compatibility
2025-08-04 14:16:33.440 [Information] X64LibraryResolver: Set environment variable: USE_PATCHED_IMPLEMENTATION = true
2025-08-04 14:16:33.441 [Information] X64LibraryResolver: Set environment variable: PHOENIX_VOCOM_ENABLED = true
2025-08-04 14:16:33.441 [Information] X64LibraryResolver: Set environment variable: VERBOSE_LOGGING = true
2025-08-04 14:16:33.442 [Information] X64LibraryResolver: Set environment variable: APCI_LIBRARY_PATH = D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 14:16:33.442 [Information] X64LibraryResolver: Set environment variable: FORCE_ARCHITECTURE_BRIDGE = true
2025-08-04 14:16:33.442 [Information] X64LibraryResolver: Added libraries path to PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 14:16:33.443 [Information] X64LibraryResolver: Library resolution completed. Success: False
2025-08-04 14:16:33.444 [Warning] IntegratedStartupService: x64 library resolution completed with issues: 
2025-08-04 14:16:33.444 [Information] IntegratedStartupService: Resolved libraries: 2
2025-08-04 14:16:33.445 [Information] IntegratedStartupService: Missing libraries: 3
2025-08-04 14:16:33.445 [Information] IntegratedStartupService: Compatible libraries: 0
2025-08-04 14:16:33.446 [Information] IntegratedStartupService: Incompatible libraries: 2
2025-08-04 14:16:33.446 [Information] IntegratedStartupService: Architecture bridge required: False
2025-08-04 14:16:33.447 [Information] IntegratedStartupService: Environment variable set: USE_PATCHED_IMPLEMENTATION = true
2025-08-04 14:16:33.447 [Information] IntegratedStartupService: Environment variable set: PHOENIX_VOCOM_ENABLED = true
2025-08-04 14:16:33.447 [Information] IntegratedStartupService: Environment variable set: VERBOSE_LOGGING = true
2025-08-04 14:16:33.448 [Information] IntegratedStartupService: Environment variable set: APCI_LIBRARY_PATH = D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 14:16:33.448 [Information] IntegratedStartupService: Environment variable set: FORCE_ARCHITECTURE_BRIDGE = true
2025-08-04 14:16:33.448 [Information] IntegratedStartupService: Recommendation: Consider building the VocomBridge project as x86 for APCI library compatibility
2025-08-04 14:16:33.450 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-08-04 14:16:33.452 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling with integrated download
2025-08-04 14:16:33.455 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-08-04 14:16:33.459 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-08-04 14:16:33.469 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-08-04 14:16:33.476 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 14:16:33.483 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 14:16:33.491 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 14:16:33.498 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 14:16:33.505 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 14:16:33.512 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 14:16:33.514 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-08-04 14:16:33.515 [Information] VCRedistBundler: About to call DownloadMissingVCRedistLibrariesAsync
2025-08-04 14:16:33.517 [Information] VCRedistBundler: Checking for missing VC++ libraries to download
2025-08-04 14:16:33.518 [Information] VCRedistBundler: Found 7 missing VC++ libraries: msvcr140.dll, api-ms-win-crt-runtime-l1-1-0.dll, api-ms-win-crt-heap-l1-1-0.dll, api-ms-win-crt-string-l1-1-0.dll, api-ms-win-crt-stdio-l1-1-0.dll, api-ms-win-crt-math-l1-1-0.dll, api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 14:16:33.520 [Information] VCRedistBundler: Attempting to download Visual C++ Redistributable package
2025-08-04 14:16:33.523 [Information] VCRedistBundler: Download attempt 1/3 from: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 14:18:00.420 [Information] VCRedistBundler: Download successful! File size: 25635768 bytes
2025-08-04 14:18:00.427 [Information] VCRedistBundler: Extracting VC++ package to: C:\Users\<USER>\AppData\Local\Temp\vcredist_extract_5d3fe0b7
2025-08-04 14:26:56.002 [Information] VCRedistBundler: Completed DownloadMissingVCRedistLibrariesAsync
2025-08-04 14:26:56.005 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-08-04 14:26:56.005 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-08-04 14:26:56.013 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-08-04 14:26:56.025 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-08-04 14:26:56.025 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-08-04 14:26:56.031 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-08-04 14:26:56.033 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 14:26:56.033 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 14:26:56.034 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 14:26:56.034 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 14:26:56.035 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 14:26:56.035 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 14:26:56.043 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-08-04 14:26:56.043 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-08-04 14:26:56.044 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-08-04 14:26:56.048 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-08-04 14:26:56.048 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-08-04 14:26:56.050 [Information] IntegratedStartupService: Checking if architecture bridge initialization is needed
2025-08-04 14:26:56.051 [Information] IntegratedStartupService: Current process architecture: x64
2025-08-04 14:26:56.052 [Warning] IntegratedStartupService: Architecture mismatch detected for apci.dll - x86 library in x64 process
2025-08-04 14:26:56.053 [Warning] IntegratedStartupService: Architecture mismatch detected for Volvo.ApciPlus.dll - x86 library in x64 process
2025-08-04 14:26:56.053 [Information] IntegratedStartupService: Architecture mismatch detected - setting up bridge environment
2025-08-04 14:26:56.054 [Warning] IntegratedStartupService: Architecture bridge executable not found at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-04 14:26:56.054 [Warning] IntegratedStartupService: Application may experience compatibility issues with x86 libraries
2025-08-04 14:26:56.056 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-08-04 14:26:56.058 [Information] LibraryExtractor: Starting library extraction process
2025-08-04 14:26:56.062 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-08-04 14:26:56.064 [Information] LibraryExtractor: Extracting embedded libraries
2025-08-04 14:26:56.067 [Information] LibraryExtractor: Copying system libraries
2025-08-04 14:26:56.073 [Information] LibraryExtractor: Checking for missing libraries to download
2025-08-04 14:26:56.074 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe
2025-08-04 14:27:19.113 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 14:27:20.118 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe (attempt 2/3)
2025-08-04 14:27:49.401 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 14:27:50.403 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe (attempt 3/3)
2025-08-04 14:28:29.744 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 14:28:29.747 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe
2025-08-04 14:29:35.316 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 14:29:36.323 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe (attempt 2/3)
2025-08-04 14:30:22.902 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 14:30:23.908 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe (attempt 3/3)
2025-08-04 14:31:07.287 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 14:31:07.292 [Warning] LibraryExtractor: Failed to download library: msvcr120.dll from all available URLs
2025-08-04 14:31:07.292 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 14:32:36.551 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-04 14:32:37.597 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-04 14:33:57.598 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-04 14:33:58.606 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-04 14:35:27.546 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-04 14:35:27.552 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-04 14:36:11.909 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-04 14:36:12.916 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-04 14:36:58.548 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-04 14:36:59.553 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-04 14:37:43.021 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-04 14:37:43.025 [Warning] LibraryExtractor: Failed to download library: msvcr140.dll from all available URLs
2025-08-04 14:37:43.025 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 14:39:39.109 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-04 14:39:40.114 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-04 14:41:27.637 [Warning] LibraryExtractor: Extraction process timed out for api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 14:41:28.640 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-04 14:43:11.949 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-04 14:43:11.955 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-04 14:43:59.672 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-04 14:44:00.676 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-04 14:44:42.132 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-04 14:44:43.137 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-04 14:45:24.351 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-04 14:45:24.356 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-runtime-l1-1-0.dll from all available URLs
2025-08-04 14:45:24.356 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 14:47:13.849 [Warning] LibraryExtractor: Extraction process timed out for api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 14:47:14.851 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-04 14:48:36.033 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-04 14:48:37.038 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-04 14:50:03.398 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-04 14:50:03.404 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-04 14:50:55.286 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-04 14:50:56.341 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-04 14:51:41.768 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-04 14:51:42.772 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-04 14:52:33.421 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-04 14:52:33.426 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-heap-l1-1-0.dll from all available URLs
2025-08-04 14:52:33.426 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 14:54:34.478 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 14:54:35.487 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-04 14:56:27.393 [Warning] LibraryExtractor: Extraction process timed out for api-ms-win-crt-string-l1-1-0.dll
2025-08-04 14:56:28.396 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-04 14:58:10.171 [Warning] LibraryExtractor: Extraction process timed out for api-ms-win-crt-string-l1-1-0.dll
2025-08-04 14:58:10.173 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-04 14:58:53.485 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 14:58:54.493 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-04 14:59:35.887 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 14:59:36.898 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-04 15:00:16.422 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 15:00:16.428 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-string-l1-1-0.dll from all available URLs
2025-08-04 15:00:16.429 [Warning] LibraryExtractor: Reached maximum download attempts (5), skipping remaining libraries to prevent hanging
2025-08-04 15:00:16.429 [Information] LibraryExtractor: Completed download phase with 5 attempts
2025-08-04 15:00:16.431 [Information] LibraryExtractor: Verifying library extraction
2025-08-04 15:00:16.432 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-08-04 15:00:16.432 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-08-04 15:00:16.433 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-08-04 15:00:16.433 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-08-04 15:00:16.433 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-08-04 15:00:16.436 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-08-04 15:00:16.438 [Information] IntegratedStartupService: Initializing dependency manager
2025-08-04 15:00:16.440 [Information] DependencyManager: Initializing dependency manager
2025-08-04 15:00:16.441 [Information] DependencyManager: Setting up library search paths
2025-08-04 15:00:16.443 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 15:00:16.443 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Drivers\Vocom
2025-08-04 15:00:16.443 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64
2025-08-04 15:00:16.444 [Information] DependencyManager: Updated PATH environment variable
2025-08-04 15:00:16.446 [Information] DependencyManager: Verifying required directories
2025-08-04 15:00:16.446 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 15:00:16.446 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Drivers\Vocom
2025-08-04 15:00:16.447 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\System
2025-08-04 15:00:16.447 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Config
2025-08-04 15:00:16.449 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-08-04 15:00:16.460 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\msvcr120.dll (x64)
2025-08-04 15:00:16.465 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\msvcp120.dll (x64)
2025-08-04 15:00:16.468 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-04 15:00:16.468 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-04 15:00:16.474 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\msvcp140.dll (x64)
2025-08-04 15:00:16.477 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\vcruntime140.dll (x64)
2025-08-04 15:00:16.477 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 15:00:16.478 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 15:00:16.479 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 15:00:16.479 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 15:00:16.480 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 15:00:16.480 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 15:00:16.481 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 15:00:16.481 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 15:00:16.482 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 15:00:16.482 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 15:00:16.483 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 15:00:16.483 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 15:00:16.484 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-04 15:00:16.484 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-04 15:00:16.485 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-04 15:00:16.485 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-04 15:00:16.486 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-04 15:00:16.487 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-04 15:00:16.487 [Information] DependencyManager: VC++ Redistributable library loading: 4/14 (28.6%) libraries loaded
2025-08-04 15:00:16.487 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-08-04 15:00:16.488 [Information] DependencyManager: Loading critical Vocom libraries
2025-08-04 15:00:16.490 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\WUDFPuma.dll (x64)
2025-08-04 15:00:16.492 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\apci.dll
2025-08-04 15:00:16.494 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-08-04 15:00:16.495 [Warning] DependencyManager: Critical library not found: apcidb.dll
2025-08-04 15:00:16.497 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\Volvo.ApciPlus.dll
2025-08-04 15:00:16.498 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-08-04 15:00:16.499 [Warning] DependencyManager: Critical library not found: Volvo.ApciPlusData.dll
2025-08-04 15:00:16.500 [Warning] DependencyManager: Critical library not found: PhoenixGeneral.dll
2025-08-04 15:00:16.501 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\msvcr120.dll (x64)
2025-08-04 15:00:16.501 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\msvcp120.dll (x64)
2025-08-04 15:00:16.502 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-08-04 15:00:16.503 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\msvcp140.dll (x64)
2025-08-04 15:00:16.503 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\vcruntime140.dll (x64)
2025-08-04 15:00:16.504 [Information] DependencyManager: Setting up environment variables
2025-08-04 15:00:16.504 [Information] DependencyManager: Environment variables configured
2025-08-04 15:00:16.506 [Information] DependencyManager: Verifying library loading status
2025-08-04 15:00:16.848 [Information] DependencyManager: Library loading verification: 5/11 (45.5%) critical libraries loaded
2025-08-04 15:00:16.849 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-08-04 15:00:16.849 [Information] DependencyManager: Dependency manager initialized successfully
2025-08-04 15:00:16.852 [Information] IntegratedStartupService: Dependency status: 7 found, 4 missing
2025-08-04 15:00:16.853 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-08-04 15:00:16.859 [Information] IntegratedStartupService: Vocom environment setup completed
2025-08-04 15:00:16.861 [Information] IntegratedStartupService: Verifying system readiness
2025-08-04 15:00:16.861 [Information] IntegratedStartupService: System readiness verification passed
2025-08-04 15:00:16.862 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-08-04 15:00:16.863 [Information] IntegratedStartupService: === System Status Summary ===
2025-08-04 15:00:16.863 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64
2025-08-04 15:00:16.864 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 15:00:16.864 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Drivers\Vocom
2025-08-04 15:00:16.864 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-08-04 15:00:16.865 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-08-04 15:00:16.865 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-08-04 15:00:16.865 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 15:00:16.866 [Information] IntegratedStartupService: === End System Status Summary ===
2025-08-04 15:00:16.866 [Information] App: Integrated startup completed successfully
2025-08-04 15:00:16.869 [Information] App: System Status - Libraries: 3 available, Dependencies: 7 loaded
2025-08-04 15:00:16.899 [Information] App: Initializing application services
2025-08-04 15:00:16.902 [Information] AppConfigurationService: Initializing configuration service
2025-08-04 15:00:16.902 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Config
2025-08-04 15:00:16.972 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Config\app_config.json
2025-08-04 15:00:16.973 [Information] AppConfigurationService: Configuration service initialized successfully
2025-08-04 15:00:16.974 [Information] App: Configuration service initialized successfully
2025-08-04 15:00:16.976 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-08-04 15:00:16.976 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-08-04 15:00:16.977 [Information] App: Environment variable exists: True, not 'false': False
2025-08-04 15:00:16.977 [Information] App: Final useDummyImplementations value: False
2025-08-04 15:00:16.978 [Information] App: Updating config to NOT use dummy implementations
2025-08-04 15:00:17.002 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Config\app_config.json
2025-08-04 15:00:17.003 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-08-04 15:00:17.003 [Information] App: usePatchedImplementation flag is: True
2025-08-04 15:00:17.003 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-08-04 15:00:17.004 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries'
2025-08-04 15:00:17.004 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-08-04 15:00:17.004 [Information] App: verboseLogging flag is: True
2025-08-04 15:00:17.007 [Information] App: Verifying real hardware requirements...
2025-08-04 15:00:17.007 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-08-04 15:00:17.007 [Information] App: ✓ Found critical library: apci.dll
2025-08-04 15:00:17.008 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-08-04 15:00:17.008 [Warning] App: ✗ Missing critical library: Volvo.ApciPlusData.dll at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\Volvo.ApciPlusData.dll
2025-08-04 15:00:17.008 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-04 15:00:17.009 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-08-04 15:00:17.009 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Drivers\Vocom\config.json
2025-08-04 15:00:17.009 [Warning] App: ⚠ Some real hardware requirements are missing - application may fall back to dummy mode
2025-08-04 15:00:17.019 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-08-04 15:00:17.021 [Information] VCRuntimeInstaller: Starting Visual C++ runtime dependency installation
2025-08-04 15:00:17.022 [Information] VCRuntimeInstaller: Process architecture: x64
2025-08-04 15:00:17.026 [Information] VCRuntimeInstaller: Found 1 missing Visual C++ runtime libraries
2025-08-04 15:00:17.030 [Information] VCRuntimeInstaller: Installing Visual C++ redistributable: vc_redist.x64.exe
2025-08-04 15:00:17.030 [Information] VCRuntimeInstaller: Attempting to download from: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 15:01:36.947 [Information] VCRuntimeInstaller: Downloaded redistributable to: C:\Users\<USER>\AppData\Local\Temp\vcredist_f26e8c11-8aeb-4931-a166-b2feff214a0d.exe
2025-08-04 15:01:36.950 [Information] VCRuntimeInstaller: Running Visual C++ redistributable installer: C:\Users\<USER>\AppData\Local\Temp\vcredist_f26e8c11-8aeb-4931-a166-b2feff214a0d.exe
2025-08-04 15:06:09.171 [Information] VCRuntimeInstaller: Visual C++ redistributable installer exit code: 0
2025-08-04 15:06:09.175 [Information] VCRuntimeInstaller: Successfully installed Visual C++ redistributable from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 15:06:09.179 [Warning] VCRuntimeInstaller: Visual C++ runtime installation partially successful. 1 libraries still missing
2025-08-04 15:06:09.179 [Warning] App: VCRuntimeInstaller had issues: 
2025-08-04 15:06:09.182 [Information] EnhancedRuntimeInstaller: Starting enhanced runtime dependency installation
2025-08-04 15:06:09.182 [Information] EnhancedRuntimeInstaller: Process architecture: x64
2025-08-04 15:06:09.187 [Warning] EnhancedRuntimeInstaller: Missing runtime library: Microsoft Visual C++ 2015-2022 Runtime (msvcr140.dll)
2025-08-04 15:06:09.188 [Information] EnhancedRuntimeInstaller: Found 1 missing runtime libraries
2025-08-04 15:06:09.191 [Information] EnhancedRuntimeInstaller: Downloading Visual C++ Redistributable (x64)
2025-08-04 15:07:28.738 [Information] EnhancedRuntimeInstaller: Installing Visual C++ Redistributable (this may take a few minutes)
2025-08-04 15:07:39.381 [Information] EnhancedRuntimeInstaller: Visual C++ Redistributable installed successfully
2025-08-04 15:07:39.397 [Information] EnhancedRuntimeInstaller: Checking Universal CRT availability
2025-08-04 15:07:39.398 [Information] EnhancedRuntimeInstaller: Windows 10+ detected - Universal CRT should be available via Windows Update
2025-08-04 15:07:39.401 [Warning] EnhancedRuntimeInstaller: Missing runtime library: Microsoft Visual C++ 2015-2022 Runtime (msvcr140.dll)
2025-08-04 15:07:39.402 [Information] EnhancedRuntimeInstaller: Runtime installation completed: 0/1 libraries resolved
2025-08-04 15:07:39.402 [Warning] App: Enhanced runtime installer also had issues, trying final fallback resolver
2025-08-04 15:07:39.405 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-08-04 15:07:39.405 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-08-04 15:07:39.409 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-08-04 15:07:39.420 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-08-04 15:09:02.076 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-04 15:09:02.077 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-08-04 15:09:02.077 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-08-04 15:09:02.078 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 15:09:02.078 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 15:09:02.079 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 15:09:02.079 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 15:09:02.080 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 15:09:02.080 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 15:09:02.080 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-08-04 15:09:02.082 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-08-04 15:09:02.082 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-08-04 15:09:02.082 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-04 15:09:02.082 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 15:09:02.083 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-08-04 15:09:02.083 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-08-04 15:09:02.083 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-08-04 15:09:02.084 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-08-04 15:09:02.093 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-08-04 15:09:02.093 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-08-04 15:09:02.096 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-08-04 15:09:02.096 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-08-04 15:09:02.097 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-08-04 15:09:02.097 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-08-04 15:09:02.099 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-08-04 15:09:02.100 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridge service immediately
2025-08-04 15:09:02.100 [Warning] ArchitectureAwareVocomServiceFactory: Architecture bridge not found at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-04 15:09:02.101 [Warning] ArchitectureAwareVocomServiceFactory: Architecture bridge not available - attempting compatibility workarounds
2025-08-04 15:09:02.102 [Information] ArchitectureAwareVocomServiceFactory: Creating compatibility Vocom service for architecture mismatch handling
2025-08-04 15:09:02.105 [Information] CompatibilityVocomService: Initializing Compatibility Vocom Service
2025-08-04 15:09:02.105 [Information] CompatibilityVocomService: This service uses WMI-based detection to avoid architecture mismatch issues
2025-08-04 15:09:02.398 [Information] CompatibilityVocomService: Compatibility Vocom Service initialized successfully
2025-08-04 15:09:02.398 [Information] ArchitectureAwareVocomServiceFactory: Compatibility Vocom service initialized successfully
2025-08-04 15:09:02.398 [Information] ArchitectureAwareVocomServiceFactory: Compatibility service created successfully
2025-08-04 15:09:02.399 [Information] App: Architecture-aware Vocom service created successfully
2025-08-04 15:09:02.399 [Information] CompatibilityVocomService: Initializing Compatibility Vocom Service
2025-08-04 15:09:02.399 [Information] CompatibilityVocomService: This service uses WMI-based detection to avoid architecture mismatch issues
2025-08-04 15:09:02.404 [Information] CompatibilityVocomService: Compatibility Vocom Service initialized successfully
2025-08-04 15:09:02.405 [Information] App: Architecture-aware Vocom service initialized successfully
2025-08-04 15:09:02.405 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-08-04 15:09:02.406 [Information] App: Checking if PTT application is running before creating Vocom service
2025-08-04 15:09:02.484 [Information] CompatibilityVocomService: Scanning for Vocom devices using WMI-based detection
2025-08-04 15:09:02.963 [Information] CompatibilityVocomService: Compatibility scan found 0 Vocom devices
2025-08-04 15:09:02.963 [Warning] App: No Vocom devices found, continuing without a connected device
2025-08-04 15:09:02.989 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-08-04 15:09:02.994 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-04 15:09:02.995 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-08-04 15:09:03.001 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-04 15:09:03.003 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-04 15:09:03.004 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-04 15:09:03.005 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 15:09:03.005 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-08-04 15:09:03.006 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 15:09:03.006 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-08-04 15:09:03.007 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-08-04 15:09:03.009 [Information] CompatibilityVocomService: Connecting to first available device (compatibility mode)
2025-08-04 15:09:03.009 [Information] CompatibilityVocomService: Scanning for Vocom devices using WMI-based detection
2025-08-04 15:09:03.373 [Information] CompatibilityVocomService: Compatibility scan found 0 Vocom devices
2025-08-04 15:09:03.373 [Warning] CompatibilityVocomService: No devices found to connect to
2025-08-04 15:09:03.374 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-08-04 15:09:03.374 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-08-04 15:09:04.376 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-08-04 15:09:04.377 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-04 15:09:04.378 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-04 15:09:04.379 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-04 15:09:04.379 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 15:09:04.379 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-08-04 15:09:04.379 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 15:09:04.380 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-08-04 15:09:04.380 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-08-04 15:09:04.380 [Information] CompatibilityVocomService: Connecting to first available device (compatibility mode)
2025-08-04 15:09:04.380 [Information] CompatibilityVocomService: Scanning for Vocom devices using WMI-based detection
2025-08-04 15:09:04.674 [Information] CompatibilityVocomService: Compatibility scan found 0 Vocom devices
2025-08-04 15:09:04.674 [Warning] CompatibilityVocomService: No devices found to connect to
2025-08-04 15:09:04.675 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-08-04 15:09:04.675 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-08-04 15:09:06.676 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-08-04 15:09:06.677 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-04 15:09:06.677 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-04 15:09:06.678 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-04 15:09:06.678 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 15:09:06.679 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-08-04 15:09:06.680 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 15:09:06.680 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-08-04 15:09:06.680 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-08-04 15:09:06.680 [Information] CompatibilityVocomService: Connecting to first available device (compatibility mode)
2025-08-04 15:09:06.681 [Information] CompatibilityVocomService: Scanning for Vocom devices using WMI-based detection
2025-08-04 15:09:07.039 [Information] CompatibilityVocomService: Compatibility scan found 0 Vocom devices
2025-08-04 15:09:07.040 [Warning] CompatibilityVocomService: No devices found to connect to
2025-08-04 15:09:07.040 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-08-04 15:09:07.040 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-08-04 15:09:10.040 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-08-04 15:09:10.041 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-08-04 15:09:10.042 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-08-04 15:09:10.044 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-08-04 15:09:10.544 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-08-04 15:09:10.545 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-08-04 15:09:10.552 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-08-04 15:09:10.554 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-08-04 15:09:10.559 [Information] BackupService: Initializing backup service
2025-08-04 15:09:10.560 [Information] BackupService: Backup service initialized successfully
2025-08-04 15:09:10.560 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-08-04 15:09:10.561 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-08-04 15:09:10.564 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-08-04 15:09:10.605 [Information] BackupService: Compressing backup data
2025-08-04 15:09:10.616 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (452 bytes)
2025-08-04 15:09:10.617 [Information] BackupServiceFactory: Created template for category: Production
2025-08-04 15:09:10.618 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-08-04 15:09:10.618 [Information] BackupService: Compressing backup data
2025-08-04 15:09:10.620 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (452 bytes)
2025-08-04 15:09:10.621 [Information] BackupServiceFactory: Created template for category: Development
2025-08-04 15:09:10.621 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-08-04 15:09:10.622 [Information] BackupService: Compressing backup data
2025-08-04 15:09:10.623 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (446 bytes)
2025-08-04 15:09:10.624 [Information] BackupServiceFactory: Created template for category: Testing
2025-08-04 15:09:10.624 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-08-04 15:09:10.625 [Information] BackupService: Compressing backup data
2025-08-04 15:09:10.626 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-08-04 15:09:10.626 [Information] BackupServiceFactory: Created template for category: Archived
2025-08-04 15:09:10.627 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-08-04 15:09:10.630 [Information] BackupService: Compressing backup data
2025-08-04 15:09:10.632 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (450 bytes)
2025-08-04 15:09:10.632 [Information] BackupServiceFactory: Created template for category: Critical
2025-08-04 15:09:10.632 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-08-04 15:09:10.633 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-08-04 15:09:10.634 [Information] BackupService: Compressing backup data
2025-08-04 15:09:10.637 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-08-04 15:09:10.637 [Information] BackupServiceFactory: Created template with predefined tags
2025-08-04 15:09:10.637 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-08-04 15:09:10.640 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-08-04 15:09:10.643 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-04 15:09:10.647 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-04 15:09:10.731 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Schedules\backup_schedules.json
2025-08-04 15:09:10.732 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-04 15:09:10.734 [Information] BackupSchedulerService: Starting backup scheduler
2025-08-04 15:09:10.734 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-08-04 15:09:10.734 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-08-04 15:09:10.736 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-08-04 15:09:10.737 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-08-04 15:09:10.741 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-08-04 15:09:10.742 [Information] App: Flash operation monitor service initialized successfully
2025-08-04 15:09:10.756 [Information] LicensingService: Initializing licensing service
2025-08-04 15:09:10.824 [Information] LicensingService: License information loaded successfully
2025-08-04 15:09:10.826 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-08-04 15:09:10.826 [Information] App: Licensing service initialized successfully
2025-08-04 15:09:10.827 [Information] App: License status: Trial
2025-08-04 15:09:10.827 [Information] App: Trial period: 30 days remaining
2025-08-04 15:09:10.828 [Information] BackupSchedulerService: Getting all backup schedules
2025-08-04 15:09:11.031 [Information] CompatibilityVocomService: Initializing Compatibility Vocom Service
2025-08-04 15:09:11.032 [Information] CompatibilityVocomService: This service uses WMI-based detection to avoid architecture mismatch issues
2025-08-04 15:09:11.049 [Information] CompatibilityVocomService: Compatibility Vocom Service initialized successfully
2025-08-04 15:09:11.101 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-08-04 15:09:11.601 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-08-04 15:09:11.651 [Information] BackupService: Initializing backup service
2025-08-04 15:09:11.651 [Information] BackupService: Backup service initialized successfully
2025-08-04 15:09:11.703 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-04 15:09:11.703 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-04 15:09:11.705 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Schedules\backup_schedules.json
2025-08-04 15:09:11.706 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-04 15:09:11.757 [Information] BackupService: Getting predefined backup categories
2025-08-04 15:09:11.808 [Information] MainViewModel: Services initialized successfully
2025-08-04 15:09:11.811 [Information] MainViewModel: Scanning for Vocom devices
2025-08-04 15:09:11.813 [Information] CompatibilityVocomService: Scanning for Vocom devices using WMI-based detection
2025-08-04 15:09:12.153 [Information] CompatibilityVocomService: Compatibility scan found 0 Vocom devices
2025-08-04 15:09:12.153 [Information] MainViewModel: Found 0 Vocom device(s)
